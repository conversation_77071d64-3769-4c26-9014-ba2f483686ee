<template>
    <FxgkTable :allTableData="allTableData" :riskTypeList="riskTypeList"></FxgkTable>
</template>

<script lang='ts' setup>
import { inject, onMounted, ref } from 'vue'
import type { Ref } from 'vue'
import FxgkTable from '@/views/risk-management/risk-alerts/components/FxgkTable.vue'
import { useRiskData } from '@/composables/useRiskData'

const socialCreditCode: Ref<string> = inject('socialCreditCode', ref(''))
const { riskTypeList, allTableData, getRiskData } = useRiskData()

onMounted(async () => {
    if (socialCreditCode.value) {
        await getRiskData(socialCreditCode.value)
    }
})
</script>

<style lang='scss' scoped>

</style>

