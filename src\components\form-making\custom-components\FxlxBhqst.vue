<template>
    <div
        style="text-align: right; margin-bottom: 16px; position: relative; z-index: 100"
    >
        <el-select v-model="selectYear" class="no-border w-150">
            <el-option
                v-for="item in ['2021', '2022', '2023', '2024', '2025']"
                :key="item"
                :value="item"
                :label="`${item}年`"
            ></el-option>
        </el-select>
    </div>
    <Fxlxbhqst
        :allTableData="allTableData"
        :riskTypeList="riskTypeList"
        :pieLoading="pieLoading"
        :selectYear="selectYear"
    />
</template>

<script lang='ts' setup>
import Fxlxbhqst from '@/views/risk-management/risk-alerts/components/Fxlxbhqst.vue'
import { ref, onMounted, inject } from 'vue'
import type { Ref } from 'vue'
import { useRiskData } from '@/composables/useRiskData'

const selectYear = ref('2025')
const socialCreditCode: Ref<string> = inject('socialCreditCode', ref(''))
const { riskTypeList, allTableData, pieLoading, getRiskData } = useRiskData()

onMounted(async () => {
    if (socialCreditCode.value) {
        await getRiskData(socialCreditCode.value)
    }
})
</script>

<style lang='scss' scoped>
</style>