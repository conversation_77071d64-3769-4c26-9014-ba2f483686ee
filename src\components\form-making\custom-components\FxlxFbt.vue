<template>
    <Fxlxfbt
        :allTableData="allTableData"
        :riskTypeList="riskTypeList"
        :pieLoading="pieLoading"
    ></Fxlxfbt>
</template>

<script lang='ts' setup>
import Fxlxfbt from '@/views/risk-management/risk-alerts/components/Fxlxfbt.vue'
import { ref, onMounted, inject } from 'vue'
import type { Ref } from 'vue'
import { useRiskData } from '@/composables/useRiskData'

const socialCreditCode: Ref<string> = inject('socialCreditCode', ref(''))
const { riskTypeList, allTableData, pieLoading, getRiskData } = useRiskData()

onMounted(async () => {
    if (socialCreditCode.value) {
        await getRiskData(socialCreditCode.value)
    }
})

</script>

<style lang='scss' scoped>
</style>