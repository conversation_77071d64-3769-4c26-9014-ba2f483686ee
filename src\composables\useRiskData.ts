import { ref, provide, inject } from 'vue'
import type { Ref } from 'vue'
import type { IGetRiskTypeData, IRiskEntListItem } from '@/types/indicator'
import indicatorService from '@/service/indicatorService'

// 定义注入的 key
export const RISK_DATA_KEY = Symbol('riskData')

// 风险数据的类型定义
export interface RiskDataState {
    riskTypeList: Ref<IGetRiskTypeData[]>
    allTableData: Ref<IRiskEntListItem[]>
    pieLoading: Ref<boolean>
    isLoaded: Ref<boolean>
    getRiskData: (socialCreditCode: string) => Promise<void>
}

// 提供风险数据的 composable
export function provideRiskData(): RiskDataState {
    const riskTypeList = ref<IGetRiskTypeData[]>([])
    const allTableData = ref<IRiskEntListItem[]>([])
    const pieLoading = ref(false)
    const isLoaded = ref(false)

    const getRiskData = async (socialCreditCode: string) => {
        if (isLoaded.value) return 

        pieLoading.value = true

        try {
            // 并行调用两个接口
            const [riskEntListRes, riskTypeDataRes] = await Promise.all([
                indicatorService.getRiskEntList({ socialCreditCode }),
                indicatorService.getRiskTypeData({ socialCreditCode }),
            ])

            // 处理企业风险列表数据
            if (riskEntListRes.success) {
                allTableData.value = riskEntListRes.data
            }

            // 处理风险类型数据
            riskTypeList.value = Object.entries(riskTypeDataRes).map(([name, value]) => ({
                name,
                value,
            }))

            isLoaded.value = true
        } catch (error) {
            console.error('获取风险数据失败:', error)
        } finally {
            pieLoading.value = false
        }
    }

    const riskDataState: RiskDataState = {
        riskTypeList,
        allTableData,
        pieLoading,
        isLoaded,
        getRiskData,
    }

    provide(RISK_DATA_KEY, riskDataState)

    return riskDataState
}

// 注入风险数据的 composable
export function useRiskData(): RiskDataState {
    const riskData = inject<RiskDataState>(RISK_DATA_KEY)

    if (!riskData) {
        throw new Error('useRiskData must be used within a component that provides risk data')
    }

    return riskData
}
